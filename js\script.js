// Animation Functions
        function animateElement(element, delay = 0) {
            setTimeout(() => {
                element.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0px)';
            }, delay);
        }

        function animateHero() {
            const heroH1 = document.querySelector('.hero-content h1');
            const heroP = document.querySelector('.hero-content p');
            const heroButton = document.querySelector('.cta-button');
            
            animateElement(heroH1, 200);
            animateElement(heroP, 400);
            animateElement(heroButton, 600);
        }

        function animateOnScroll() {
            const elements = document.querySelectorAll('.js-animate');
            
            elements.forEach((element, index) => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;
                
                if (elementTop < window.innerHeight - elementVisible) {
                    if (!element.classList.contains('animated')) {
                        element.classList.add('animated');
                        
                        // Add staggered animation for grid items
                        if (element.classList.contains('skill-card') || 
                            element.classList.contains('portfolio-item')) {
                            const gridItems = element.parentElement.querySelectorAll('.js-animate');
                            const itemIndex = Array.from(gridItems).indexOf(element);
                            animateElement(element, itemIndex * 100);
                        } else {
                            animateElement(element, 0);
                        }
                    }
                }
            });
        }

        // Advanced hover animations
        function setupHoverAnimations() {
            const skillCards = document.querySelectorAll('.skill-card');
            const portfolioItems = document.querySelectorAll('.portfolio-item');
            
            [...skillCards, ...portfolioItems].forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transition = 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                    this.style.transform = 'translateY(-15px) scale(1.03)';
                    this.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.2)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
                });
            });
        }

        // Parallax effect for hero section
        function setupParallax() {
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const hero = document.querySelector('.hero');
                const heroContent = document.querySelector('.hero-content');
                
                if (hero && heroContent) {
                    heroContent.style.transform = `translateY(${scrolled * 0.5}px)`;
                }
            });
        }

        // Smooth scrolling for navigation links
        function setupSmoothScrolling() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const headerHeight = document.querySelector('header').offsetHeight;
                        const targetPosition = target.offsetTop - headerHeight;
                        
                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }

        // Header background animation on scroll
        function setupHeaderAnimation() {
            const header = document.querySelector('header');
            let lastScrollY = window.scrollY;
            
            window.addEventListener('scroll', () => {
                const currentScrollY = window.scrollY;
                
                if (currentScrollY > 100) {
                    header.style.background = 'rgba(255, 255, 255, 0.98)';
                    header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
                    header.style.transform = 'translateY(0)';
                } else {
                    header.style.background = 'rgba(255, 255, 255, 0.95)';
                    header.style.boxShadow = 'none';
                }
                
                // Hide header when scrolling down, show when scrolling up
                if (currentScrollY > lastScrollY && currentScrollY > 200) {
                    header.style.transform = 'translateY(-100%)';
                } else {
                    header.style.transform = 'translateY(0)';
                }
                
                lastScrollY = currentScrollY;
            });
        }

        // Button pulse animation
        function setupButtonAnimations() {
            const buttons = document.querySelectorAll('.cta-button, .contact-button');
            
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.animation = 'pulse 0.6s ease-in-out';
                });
                
                button.addEventListener('animationend', function() {
                    this.style.animation = '';
                });
            });
        }

        // Skill icons rotation on hover
        function setupSkillIconAnimations() {
            const skillIcons = document.querySelectorAll('.skill-icon');
            
            skillIcons.forEach(icon => {
                icon.addEventListener('mouseenter', function() {
                    this.style.transition = 'transform 0.5s ease';
                    this.style.transform = 'rotate(360deg) scale(1.1)';
                });
                
                icon.addEventListener('mouseleave', function() {
                    this.style.transform = 'rotate(0deg) scale(1)';
                });
            });
        }

        // Portfolio image hover effects
        function setupPortfolioImageEffects() {
            const portfolioImages = document.querySelectorAll('.portfolio-image');
            
            portfolioImages.forEach(image => {
                image.addEventListener('mouseenter', function() {
                    this.style.transition = 'all 0.4s ease';
                    this.style.transform = 'scale(1.05)';
                    this.style.filter = 'brightness(1.1)';
                });
                
                image.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.filter = 'brightness(1)';
                });
            });
        }

        // Initialize all animations
        document.addEventListener('DOMContentLoaded', function() {
            // Start hero animations immediately
            setTimeout(animateHero, 500);
            
            // Setup all other animations
            setupHoverAnimations();
            setupParallax();
            setupSmoothScrolling();
            setupHeaderAnimation();
            setupButtonAnimations();
            setupSkillIconAnimations();
            setupPortfolioImageEffects();
            
            // Setup scroll animations
            window.addEventListener('scroll', animateOnScroll);
            
            // Run once to catch elements already in view
            animateOnScroll();
        });

        // Add CSS keyframes for pulse animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);